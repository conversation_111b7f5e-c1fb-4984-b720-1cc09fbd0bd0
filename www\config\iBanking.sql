SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
SET GLOBAL time_zone = '+07:00';
SET GLOBAL event_scheduler = ON;

DROP DATABASE IF EXISTS `iBanking`;
CREATE DATABASE `iBanking` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE `iBanking`;

CREATE TABLE `accounts` (
    `studentID` VARCHAR(8) NOT NULL,
    `username` VARCHAR(255) NOT NULL,
    `password` VARCHAR(255) NOT NULL,
    PRIMARY KEY (`studentID`)
);

CREATE TABLE `profiles` (
    `studentID` VARCHAR(8) NOT NULL,
    `fullname` VARCHAR(255) NOT NULL,
    `email` VARCHAR(255) NOT NULL,
    `phone` VARCHAR(20) NOT NULL,
    `balance` INT UNSIGNED NOT NULL DEFAULT 0,
    `tuition` INT UNSIGNED NOT NULL,
    FOREIGN KEY (`studentID`) REFERENCES `accounts`(`studentID`)
);

CREATE TABLE `transactions` (
    `transactionID` INT UNSIGNED NOT NULL AUTO_INCREMENT,
    `payer` VARCHAR(8) NOT NULL,
    `payee` VARCHAR(8) NOT NULL,
    `amount` INT NOT NULL,
    `date` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `status` ENUM('pending', 'approved') NOT NULL DEFAULT 'pending',
    PRIMARY KEY (`transactionID`),
    FOREIGN KEY (`payer`) REFERENCES `accounts`(`studentID`),
    FOREIGN KEY (`payee`) REFERENCES `accounts`(`studentID`)
);

CREATE TABLE `OTP` (
    `transactionID` INT UNSIGNED NOT NULL,
    `OTP` INT UNSIGNED NOT NULL UNIQUE,
    `expired` DATETIME NOT NULL,
    FOREIGN KEY (`transactionID`) REFERENCES `transactions`(`transactionID`) ON DELETE CASCADE
);

INSERT INTO `accounts` (`studentID`, `username`, `password`) VALUES
('523H0076', '523H0076', '$2y$10$ve4EFuIiVtioHk6nonSdeOKemqmqXf9BRB/ZJZSTVjrZalrmr9eQm'),
('523H0042', '523H0042', '$2y$10$ve4EFuIiVtioHk6nonSdeOKemqmqXf9BRB/ZJZSTVjrZalrmr9eQm');

INSERT INTO `profiles` (`studentID`, `fullname`, `email`, `phone`, `balance`, `tuition`) VALUES
('523H0076', 'Nguyen Van A', '<EMAIL>', '**********', ********, 8000000),
('523H0042', 'Tran Thi B', '<EMAIL>', '**********', ********, ********);

DELIMITER $$
-- SCHEDULE EVENT
CREATE EVENT delete_expired_transactions
ON SCHEDULE EVERY 15 SECOND
DO
BEGIN
    DELETE t
    FROM `transactions` t
    JOIN `OTP` o ON t.`transactionID` = o.`transactionID`
    WHERE t.`status` = 'pending'
    AND o.`expired` < NOW();
END$$

-- TRIGGER
CREATE TRIGGER check_amount_and_pending_transaction
BEFORE INSERT ON `transactions`
FOR EACH ROW
BEGIN
    DECLARE maxTuition, pendingTransactions INT;
    SELECT `tuition` INTO maxTuition FROM `profiles` WHERE `studentID` = NEW.`payer`;
    IF NEW.`amount` > maxTuition THEN
        SIGNAL SQLSTATE '45000'
        SET MESSAGE_TEXT = 'Amount exceeds tuition fee';
    END IF;
    
    SELECT COUNT(*) INTO pendingTransactions FROM `transactions` WHERE `payee` = NEW.`payee` AND `status` = 'pending';
    IF pendingTransactions > 0 THEN
        SIGNAL SQLSTATE '45000'
        SET MESSAGE_TEXT = 'Pending transaction found';
    END IF;
END$$
DELIMITER ;