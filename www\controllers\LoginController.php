<?php
require_once ROOT.'/models/LoginModel.php';

Class LoginController {
    private $loginModel;

    public function __construct() {
        $this->loginModel = new LoginModel();
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }
    }

    public function index() {
        require_once ROOT."/views/login.php";
    }

    public function login() {
        $username = $_POST['username'];
        $password = $_POST['password'];
        
        $result = $this->loginModel->login($username, $password);
        echo json_encode($result);
    }

    public function logout() {
        session_destroy();
        header("Location: /login");
    }
}
?>