<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Login Page</title>
  <script src="https://cdn.jsdelivr.net/npm/@tailwindcss/browser@4"></script>
  <style>
    @keyframes fadeIn {
      from { opacity: 0; transform: translateY(10px); }
      to { opacity: 1; transform: translateY(0); }
    }
    .animate-fadeIn { animation: fadeIn 0.6s ease-out; }

    @keyframes bounceSlow {
      0%, 100% { transform: translateY(0); }
      50% { transform: translateY(-5px); }
    }
    .animate-bounce-slow { animation: bounceSlow 3s infinite; }

    @keyframes glowPulse {
      0%, 100% { box-shadow: 0 0 0px rgba(99,102,241,0.6); transform: translateY(0); }
      50% { box-shadow: 0 0 20px rgba(99,102,241,0.8); transform: translateY(-1px); }
    }
    .hover\:animate-glow:hover { animation: glowPulse 1s infinite; }

    @keyframes shake {
      0%, 100% { transform: translateX(0); }
      20%, 60% { transform: translateX(-6px); }
      40%, 80% { transform: translateX(6px); }
    }
    .animate-shake { animation: shake 0.4s; }
  </style>
</head>
<body class="min-h-screen relative flex items-center justify-center">

  <!-- Background -->
  <div class="absolute inset-0 bg-cover bg-center" style="background-image: url('image/background.jpg');"></div>
  <div class="absolute inset-0 bg-gradient-to-r from-gray-700/70 via-slate-600/70 to-gray-900/70"></div>

  <!-- Login card -->
  <div class="relative w-full max-w-md bg-white rounded-2xl shadow-2xl p-8 
              transform transition duration-500 hover:scale-[1.02] animate-fadeIn">

    <!-- Logo -->
    <div class="text-center mb-8">
      <div class="w-20 h-20 mx-auto mb-3 rounded-full overflow-hidden shadow-md animate-bounce-slow">
        <img src="image/investment.png" alt="Logo" class="w-full h-full object-cover">
      </div>
      <h1 class="text-3xl font-bold text-gray-800">Welcome Back</h1>
      <p class="text-gray-500 text-sm">Please login to your account</p>
    </div>

    <!-- Error box -->
    <div id="globalError" class="hidden mb-4 p-3 rounded-xl bg-red-100 text-red-600 text-sm font-medium"></div>

    <!-- Form -->
    <form id="loginForm" class="space-y-5">
      <!-- Username -->
      <div>
        <label for="username" class="block text-sm font-medium text-gray-700">Username</label>
        <input type="text" id="username" name="username" placeholder="Enter your username"
          class="mt-1 w-full px-4 py-2 border border-gray-300 rounded-xl shadow-sm 
                 focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 outline-none transition">
        <p id="usernameError" class="hidden text-red-500 text-sm mt-1"></p>
      </div>

      <!-- Password -->
      <div class="relative">
        <label for="password" class="block text-sm font-medium text-gray-700">Password</label>

        <div class="relative">
          <input type="password" id="password" name="password" placeholder="********"
            class="mt-1 w-full h-11 px-4 border border-gray-300 rounded-xl shadow-sm 
                  focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 outline-none transition pr-10">

          <!-- Toggle button -->
          <button type="button" onclick="togglePassword()" id="toggleBtn"
            class="absolute top-1/2 right-3 -translate-y-1/2 text-gray-400 hover:text-gray-600 transition-transform duration-200 active:scale-90">
            <!-- Eye Open -->
            <svg id="eyeOpen" xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" 
                viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
              <path stroke-linecap="round" stroke-linejoin="round" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
              <path stroke-linecap="round" stroke-linejoin="round" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.477 0 8.268 2.943 9.542 7-1.274 4.057-5.065 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
            </svg>
            <!-- Eye Closed -->
            <svg id="eyeClosed" xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 hidden" fill="none" 
                viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
              <path stroke-linecap="round" stroke-linejoin="round" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.477 0-8.268-2.943-9.542-7a9.953 9.953 0 012.956-4.568M6.18 6.18A9.955 9.955 0 0112 5c4.477 0 8.268 2.943 9.542 7a9.956 9.956 0 01-4.132 5.411M6.18 6.18L3 3m3.18 3.18l12.64 12.64M9.88 9.88a3 3 0 104.24 4.24" />
            </svg>
          </button>
        </div>

        <!-- Error -->
        <p id="passwordError" class="hidden text-red-500 text-sm mt-1"></p>
      </div>

      <!-- Submit -->
      <button type="submit"
        class="w-full bg-indigo-600 text-white font-semibold py-2 px-4 rounded-xl shadow-md 
               hover:bg-indigo-700 active:scale-95 transition hover:animate-glow">
        Sign In
      </button>
    </form>
    </div>

  <script>
    const username = document.getElementById("username");
    const password = document.getElementById("password");
    const usernameError = document.getElementById("usernameError");
    const passwordError = document.getElementById("passwordError");
    const globalError = document.getElementById("globalError");

    // Validate on submit
    document.getElementById("loginForm").addEventListener("submit", async (e) => {
      e.preventDefault();

      let valid = true;
      usernameError.classList.add("hidden");
      passwordError.classList.add("hidden");
      globalError.classList.add("hidden");
      username.classList.remove("border-red-500");
      password.classList.remove("border-red-500");

      if (username.value.trim() === "") {
        usernameError.textContent = "Please enter your username";
        usernameError.classList.remove("hidden");
        username.classList.add("border-red-500", "animate-shake");
        valid = false;
      }
      if (password.value.trim() === "") {
        passwordError.textContent = "Password is required";
        passwordError.classList.remove("hidden");
        password.classList.add("border-red-500", "animate-shake");
        valid = false;
      }

      setTimeout(() => {
        username.classList.remove("animate-shake");
        password.classList.remove("animate-shake");
      }, 500);

      if (!valid) return;

      try {
        const formData = new FormData(e.target);
        const response = await fetch("login?method=login", {
          method: "POST",
          body: formData
        });
        const result = await response.json();

        if (result.success) {
          window.location.href = "home"; 
        } else {
          globalError.textContent = result.message || "Incorrect username or password";
          globalError.classList.remove("hidden");
        }
      } catch (err) {
        console.error("Error:", err);
        globalError.textContent = "An error occurred, please try again later.";
        globalError.classList.remove("hidden");
      }
    });

    // Remove error while typing
    username.addEventListener("input", () => {
      if (username.value.trim() !== "") {
        usernameError.classList.add("hidden");
        username.classList.remove("border-red-500");
      }
    });
    password.addEventListener("input", () => {
      if (password.value.trim() !== "") {
        passwordError.classList.add("hidden");
        password.classList.remove("border-red-500");
      }
    });

    // Toggle password visibility
    function togglePassword() {
      const eyeOpen = document.getElementById("eyeOpen");
      const eyeClosed = document.getElementById("eyeClosed");

      if (password.type === "password") {
        password.type = "text";
        eyeOpen.classList.add("hidden");
        eyeClosed.classList.remove("hidden");
      } else {
        password.type = "password";
        eyeOpen.classList.remove("hidden");
        eyeClosed.classList.add("hidden");
      }
    }
  </script>
</body>
</html>
