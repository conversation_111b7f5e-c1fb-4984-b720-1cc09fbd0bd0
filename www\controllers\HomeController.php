<?php
require_once ROOT.'/models/HomeModel.php';

Class HomeController {
    private $homeModel;

    public function __construct() {
        $this->homeModel = new HomeModel();
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }
    }

    public function index() {
        require_once ROOT."/views/home.php";
    }

    public function getStudentID() {
        echo json_encode(['studentID' => $_SESSION['studentID']]);
    }

    public function getStudentInfo() {
        $studentID = $_POST['studentID'] ?? null;

        $result = $this->homeModel->getStudentInfo($studentID);
        echo json_encode($result);
        exit; 
    }
}
?>
