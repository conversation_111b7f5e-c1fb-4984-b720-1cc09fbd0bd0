<?php
require_once("DB.php");

Class LoginModel {
    private $con;

    public function __construct() {
        $this->con = DB::connect();
        if (session_status() == PHP_SESSION_NONE) {
            session_start();
        }
    }

    public function login($username, $password) {
        try {
            $sql = "SELECT * FROM `accounts` WHERE `username` = ?";
            $stmt = $this->con->prepare($sql);
            $stmt->bind_param("s", $username);
            $stmt->execute();
            $result = $stmt->get_result();

            if ($result->num_rows == 0) {
                throw new Exception('Username or password is incorrect');
            }

            $row = $result->fetch_assoc();
            if (password_verify($password, $row['password'])) {
                $_SESSION['studentID'] = $row['studentID'];
                return ['success' => true];
            } else {
                throw new Exception('Username or password is incorrect');
            }

        } catch (Exception $e) {
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }
}
?>