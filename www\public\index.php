<?php
// '/var/www' will be a ROOT
date_default_timezone_set('Asia/Ho_Chi_Minh');
defined('ROOT') or define('ROOT', realpath(__DIR__ . '/..'));
require_once ROOT."/config/routes.php";

// Get the URI and route
$uri = parse_url($_SERVER['REQUEST_URI']);
$path = trim($uri['path'], '/');
$segments = explode('/', $path);

// Controller mặc định = home
$controller = $segments[0] ?? 'home';
// Method mặc định = index
$queries['method'] = $segments[1] ?? 'index';

// Nếu có query string thì merge thêm vào
if (isset($uri['query'])) {
    parse_str($uri['query'], $queryParams);
    $queries = array_merge($queries, $queryParams);
}

route($controller, $queries);
