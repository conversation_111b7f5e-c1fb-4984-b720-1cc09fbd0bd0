<?php
require_once ROOT.'/models/OTPModel.php';
require_once ROOT.'/models/TuitionModel.php';

Class OTPController {
    private $OTPModel;
    private $tuitionModel;

    public function __construct() {
        $this->OTPModel = new OTPModel();
        $this->tuitionModel = new TuitionModel();
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }
    }

    public function index() {
        require_once ROOT."/views/home.php";
    }

    public function generateOTP() {
        $transactionID = $_POST['transactionID'];
        $timeLife = $_POST['timeLife'] ?? 5;

        $result = $this->OTPModel->generateOTP($transactionID, $timeLife);
        echo json_encode($result);
    }

    public function verifyOTP() {
        $transactionID = $_POST['transactionID'];
        $OTP = $_POST['OTP'];

        $verifyResult = $this->OTPModel->verifyOTP($transactionID, $OTP);

        if ($verifyResult['success']) {
            $updateResult = $this->tuitionModel->updateBalanceAndTuition($transactionID);
            echo json_encode($updateResult);
        } else {
            echo json_encode($verifyResult);
        }
    }
}
?>