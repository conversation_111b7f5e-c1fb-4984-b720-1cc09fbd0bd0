FROM php:8.1-apache

# Ghi đè file c<PERSON>u hình <PERSON> (000-default.conf)
COPY ./www/config/apache/000-default.conf /etc/apache2/sites-available/000-default.conf

# Cài các extension PHP cần thiết
RUN docker-php-ext-install mysqli && docker-php-ext-enable mysqli

# Cài các công cụ hệ thống cần cho Composer
RUN apt-get update && apt-get install -y \
    unzip \
    zip \
    curl \
    git \
    && apt-get clean && rm -rf /var/lib/apt/lists/*

# Cài Composer toàn cục
RUN curl -sS https://getcomposer.org/installer | php && \
    mv composer.phar /usr/local/bin/composer && \
    chmod +x /usr/local/bin/composer

# Cài đặt thư viện PHP
RUN if [ -f /var/www/composer.json ] && [ -f /var/www/composer.lock ]; then \
    composer install --working-dir=/var/www/; \
    fi

# Cài mod_rewrite cho Apache
RUN a2enmod rewrite

# EXPOSE cổng cho Apache và WebSocket (tùy bạn có dùng)
EXPOSE 80