services:
    web:
        build: .
        ports:
            - 8080:80
        volumes:
            - ./www/public:/var/www/html/
            - ./www:/var/www/
        depends_on:
            - "mysql-server"

    mysql-server:
        image: mysql:8.4.2
        platform: linux/x86_64 # thêm lệnh này để chạy được trên Mac <PERSON>, nếu lỗi trên chip Intel thì xóa ra
        ports:
            - 3399:3306 # nếu muốn đổi thì chỉ cần đổi số 3399, không được đổi 3306
        restart: always
        volumes:
            - ./data:/var/lib/mysql # nếu để dòng này thì dòng bên dưới sẽ không có tác dụng
            - ./www/config/:/docker-entrypoint-initdb.d/
        environment:
            MYSQL_ROOT_PASSWORD: root
            MYSQL_USER: user
            MYSQL_PASSWORD: user
            TZ: 'Asia/Ho_Chi_Minh'

    phpmyadmin:
        image: phpmyadmin/phpmyadmin:latest
        ports:
            - 8888:80
        restart: always
        environment:
            - PMA_HOST=mysql-server
        depends_on:
            - "mysql-server"