<?php
require_once("DB.php");

Class HomeModel {
    private $con;

    public function __construct() {
        $this->con = DB::connect();
        if (session_status() == PHP_SESSION_NONE) {
            session_start();
        }
    }

    public function getStudentInfo($studentID) {
        try {
            $sql = "SELECT studentID, fullname, email, phone, tuition, balance 
                    FROM `profiles` 
                    WHERE `studentID` = ?";
            $stmt = $this->con->prepare($sql);
            $stmt->bind_param("s", $studentID);
            $stmt->execute();
            $result = $stmt->get_result();

            if ($result->num_rows == 0) {
                throw new Exception('Student not found');
            }
            $row = $result->fetch_assoc();

            return ['success' => true, 'data' => $row];

        } catch (Exception $e) {
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }
}
?>