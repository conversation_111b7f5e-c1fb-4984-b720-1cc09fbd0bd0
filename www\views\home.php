<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title>iBanking - Tuition Payment</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <!-- Import Lucide -->
  <script src="https://unpkg.com/lucide@latest"></script>
  <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.7.1/jquery.min.js"></script>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/7.0.1/css/all.min.css">
  <style>
    .hover-glow:hover {
      transform: scale(1.03);
      box-shadow: 0 0 25px rgba(34,197,94,0.5);
      transition: all 0.3s ease-in-out;
    }
  </style>
</head>
<body class="min-h-screen flex flex-col font-sans bg-cover bg-center bg-no-repeat relative" 
      style="background-image: url('image/payment.jpg');">

  <!-- Overlay Gradient  -->
  <div class="absolute inset-0 bg-gradient-to-br from-green-800/80 to-green/80"></div>

  <!-- Header -->
  <header class="relative bg-gradient-to-r from-green-800 to-black text-white shadow-lg z-10">
    <div class="max-w-7xl mx-auto px-6 py-4 flex justify-between items-center">
      <div class="flex items-center space-x-2 group">
        <i data-lucide="landmark" class="w-7 h-7 text-green-300 transition transform group-hover:scale-110 group-hover:text-green-400"></i>
        <h1 class="text-2xl font-bold tracking-wide">iBanking</h1>
      </div>
      <nav>
        <ul class="flex space-x-6 font-medium">
          <li><a href="/home" class="hover:text-green-300 transition">Home</a></li>
          <li><a href="/login?method=logout" class="hover:text-green-300 transition">Logout</a></li>
        </ul>
      </nav>
    </div>
  </header>

  <!-- Main -->
  <main class="relative flex-grow flex items-center justify-center px-4 py-12 z-10">
    <div class="w-full max-w-4xl bg-white/90 backdrop-blur-sm p-10 rounded-3xl shadow-2xl space-y-8">
      <h2 class="text-3xl font-bold text-gray-900 text-center mb-6">
        Tuition Fee Payment
      </h2>

      <form action="/tuition" method="POST">

        <!-- Payer Information -->
        <div class="bg-gradient-to-br from-green-50 to-gray-100 border border-green-200 rounded-2xl p-6 shadow-lg hover-glow mb-3">
          <h3 class="text-lg font-semibold text-green-800 mb-4 flex items-center space-x-2 group">
            <i data-lucide="user-circle" class="w-5 h-5 text-green-700 transition transform group-hover:scale-110 group-hover:text-green-900"></i>
            <span>Payer Information</span>
          </h3>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <!-- Full Name -->
            <div>
              <label class="block text-sm font-medium text-gray-600">Full Name</label>
              <input type="text" name="payer_name"
                     class="mt-1 block w-full rounded-lg shadow-sm bg-gray-100" readonly>
            </div>

            <!-- Phone -->
            <div>
              <label class="block text-sm font-medium text-gray-600">Phone Number</label>
              <input type="text" name="payer_phone"
                     class="mt-1 block w-full rounded-lg shadow-sm bg-gray-100" readonly>
            </div>

            <!-- Email -->
            <div class="md:col-span-2">
              <label class="block text-sm font-medium text-gray-600">Email</label>
              <input type="email" name="payer_email"
                     class="mt-1 block w-full rounded-lg shadow-sm bg-gray-100" readonly>
            </div>
          </div>
        </div>

        <!-- Tuition Information -->
        <div class="bg-gradient-to-br from-green-50 to-gray-100 border border-green-200 rounded-2xl p-6 shadow-lg hover-glow mb-3">
          <h3 class="text-lg font-semibold text-green-800 mb-4 flex items-center space-x-2 group">
            <i data-lucide="book" class="w-5 h-5 text-green-700 transition transform group-hover:scale-110 group-hover:text-green-900"></i>
            <span>Tuition Information</span>
          </h3>

          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">

            <!-- Student ID -->
            <div id="student_id_div" class="md:col-span-2">
              <label class="block text-sm font-medium text-gray-600">Student ID</label>
              <input type="text" id="student_id" name="payee" placeholder="Enter Student ID" 
                    class="mt-1 block w-full border border-gray-300 rounded-lg shadow-sm focus:ring-green-500 focus:border-green-500" required>
            </div>

            <!-- Student Name & Tuition Amount -->
            <div id="student_name_div">
              <label class="block text-sm font-medium text-gray-600">Student Name</label>
              <input type="text" id="student_name" name="student_name" placeholder="Auto-filled after entering Student ID" 
                    class="mt-1 block w-full border border-gray-300 rounded-lg shadow-sm bg-gray-100" readonly>
            </div>
            <div id="tuition_amount_div">
              <label class="block text-sm font-medium text-gray-600">Tuition Amount</label>
              <input type="text" id="tuition_amount" name="tuition_amount" placeholder="Auto-filled after entering Student ID" 
                    class="mt-1 block w-full border border-gray-300 rounded-lg shadow-sm bg-gray-100" readonly>
            </div>
          </div>
        </div>


        <!-- Payment Information -->
        <div class="bg-gradient-to-br from-green-50 to-gray-100 border border-green-200 rounded-2xl p-6 shadow-lg hover-glow mb-3">
          <h3 class="text-lg font-semibold text-green-800 mb-4 flex items-center space-x-2 group">
            <i data-lucide="credit-card" class="w-5 h-5 text-green-700 transition transform group-hover:scale-110 group-hover:text-green-900"></i>
            <span>Payment Information</span>
          </h3>

          <!-- Available Balance & Payment Amount -->
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label class="block text-sm font-medium text-gray-600">Available Balance</label>
              <input type="text" name="available_balance"
                     class="mt-1 block w-full border border-gray-300 rounded-lg shadow-sm bg-gray-100" readonly>
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-600">Payment Amount</label>
              <input type="text" name="payment_amount" placeholder="Auto-filled after entering Student ID"
                     class="mt-1 block w-full border border-gray-300 rounded-lg shadow-sm bg-gray-100" readonly>
            </div>
          </div>

          <!-- Payment Status -->
          <div id="sufficient_balance" class="text-green-600 mt-1 hidden">
            <i class="fa-solid fa-check"></i>
            <span>Payment allowed - Balance is sufficient</span>
          </div>
          <div id="insufficient_balance" class="text-red-600 mt-1 hidden">
            <i class="fa-solid fa-xmark"></i>
            <span>Payment not allowed - Balance is insufficient</span>
          </div>

          <!-- Terms and Conditions -->
          <div class="mt-4 flex items-center">
            <input id="terms" type="checkbox"
                   class="h-4 w-4 text-green-600 border border-gray-300 rounded" required>
            <label for="terms" class="ml-2 block text-sm text-gray-600">
              I agree to the Terms and Conditions
            </label>
          </div>
        </div>

        <!-- Confirm Button -->
        <div class="text-center mt-8">
          <button type="submit" 
                  class="bg-gradient-to-r from-green-700 to-black text-white px-6 py-3 rounded-xl shadow-lg hover:scale-105 transition transform disabled:opacity-50 flex items-center justify-center mx-auto space-x-2">
            <i data-lucide="check-circle" class="w-5 h-5 animate-pulse"></i>
            <span>Confirm Transaction</span>
          </button>
        </div>

      </form>
    </div>
  </main>

  <!-- Footer -->
  <footer class="relative bg-gradient-to-r from-green-900 to-black text-gray-200 text-center py-6 border-t z-10">
    <p>&copy; 2025 iBanking. All rights reserved.</p>
  </footer>

  <script>lucide.createIcons();</script>
  <script src="/js/views/home.js"></script>
</body>
</html>
