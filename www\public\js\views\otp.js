$(document).ready(async function() {
    let transactionID = await (await fetch("tuition?method=generateTransaction", {method: "POST", body: form})).json();
    let 
});

// Auto-focus next input and backspace navigation
const inputs = document.querySelectorAll('input[name^="otp"]');
inputs.forEach((input, i) => {
    input.addEventListener('input', () => {
        if (input.value.length === 1 && i < inputs.length - 1) {
            inputs[i + 1].focus();
        }
    });
    input.addEventListener('keydown', (e) => {
        if (e.key === "Backspace" && input.value === "" && i > 0) {
            inputs[i - 1].focus();
        }
    });
});

// OTP Countdown Timer with red warning under 1 min
let timerDuration = 5 * 60; // 5 minutes in seconds
const timerEl = document.getElementById('otp-timer');
const resendBtn = document.getElementById('resend-btn');

function startCountdown() {
    const interval = setInterval(() => {
        const minutes = Math.floor(timerDuration / 60);
        const seconds = timerDuration % 60;
        timerEl.textContent = `${minutes.toString().padStart(2,'0')}:${seconds.toString().padStart(2,'0')}`;

        // Change color to red if less than 1 min
        if (timerDuration <= 60 && timerDuration > 0) {
            timerEl.classList.remove('text-gray-600');
            timerEl.classList.add('text-red-600', 'font-bold');
        }

        if (timerDuration <= 0) {
        clearInterval(interval);
            timerEl.textContent = "00:00";
            resendBtn.classList.remove('hidden');
        } else {
            timerDuration--;
        }
    }, 1000);
}

startCountdown();

// Resend OTP button click
resendBtn.addEventListener('click', () => {

    // Call backend API to resend OTP 
    alert('A new OTP has been sent!');
    timerDuration = 5 * 60;
    resendBtn.classList.add('hidden');
    timerEl.classList.remove('text-red-600', 'font-bold');
    timerEl.classList.add('text-gray-600');
    startCountdown();
});