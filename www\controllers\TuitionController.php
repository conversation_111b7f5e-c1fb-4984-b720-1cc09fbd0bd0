<?php
require_once ROOT.'/models/TuitionModel.php';
require_once ROOT.'/models/OTPModel.php';

Class TuitionController {
    private $tuitionModel;
    private $OTPModel;

    public function __construct() {
        $this->tuitionModel = new TuitionModel();
        $this->OTPModel = new OTPModel();
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }
    }

    public function index() {
        require_once ROOT."/views/home.php";
    }

    public function updateBalanceAndTuition() {
        $transactionID = $_POST['transactionID'] ?? null;

        if (!$transactionID) {
            echo json_encode(['success' => false, 'message' => 'Missing transaction ID']);
            return;
        }

        $result = $this->tuitionModel->updateBalanceAndTuition($transactionID);
        echo json_encode($result);
    }

    public function generateTransaction() {
        $studentID = $_POST['studentID'];
        $payee = $_POST['payee'];
        $amount = $_POST['amount'];

        $result = $this->tuitionModel->generateTransaction($studentID, $payee, $amount);
        echo json_encode($result);
    }
}
?>
