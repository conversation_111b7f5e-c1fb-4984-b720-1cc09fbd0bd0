let availableBalance;

$(document).ready(async function() {
    let studentID = (await (await fetch("home?method=getStudentID")).json()).studentID;
    let form = new FormData();
    form.append("studentID", studentID);
    let result = await (await fetch("home?method=getStudentInfo", {method: "POST", body: form})).json();

    if (result.success) {
    let student = result.data;
        $("input[name='payer_name']").val(student.fullname);
        $("input[name='payer_phone']").val(student.phone);
        $("input[name='payer_email']").val(student.email);
        $("input[name='available_balance']").val(student.balance.toLocaleString('vi-VN') + " VND");
        availableBalance = student.balance;
    }
});

$("input[name='student_id']").on("input", async function() {
    let studentID = $(this).val();
    if (studentID.trim() === "" || studentID.length < 8) {
        resetError($("input[name='student_name']"));
        resetError($("input[name='tuition_amount']"));
        resetError($("input[name='payment_amount']"));
        checkBalance(-1);
        return;
    }

    let form = new FormData();
    form.append("studentID", studentID);
    let result = await (await fetch("home?method=getStudentInfo", {method: "POST", body: form})).json();
    if (result.success) {
    let student = result.data;
        $("input[name='student_name']").val(student.fullname);
        $("input[name='tuition_amount']").val(student.tuition.toLocaleString('vi-VN') + " VND");
        $("input[name='payment_amount']").val(student.tuition.toLocaleString('vi-VN') + " VND");
        checkBalance(student.tuition);
    } else {
        makeError($("input[name='student_name']"), "Student not found");
        makeError($("input[name='tuition_amount']"), "0 VND");
        resetError($("input[name='payment_amount']"));
        checkBalance(-1);
    }
});

function checkBalance(paymentAmount) {
    if (paymentAmount === -1) {
        $("#sufficient_balance").addClass("hidden");
        $("#insufficient_balance").addClass("hidden");
        return;
    }

    if (availableBalance >= paymentAmount) {
        $("#sufficient_balance").removeClass("hidden");
    } else {
        $("#insufficient_balance").removeClass("hidden");
    }
};

function makeError($element, message) {
    $element.val(message);
    $element.removeClass("border-gray-300");
    $element.addClass("border-red-500");

    if ($element.attr("name") === "student_name") {
        $element.removeClass("text-gray-600");
        $element.addClass("text-red-600");
    }
}

function resetError($element) {
    $element.val("");
    $element.removeClass("border-red-500");
    $element.addClass("border-gray-300");

    if ($element.attr("name") === "student_name") {
        $element.removeClass("text-red-600");
        $element.addClass("text-gray-600");
    }
}