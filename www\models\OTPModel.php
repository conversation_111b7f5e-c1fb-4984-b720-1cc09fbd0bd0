<?php
require_once("DB.php");

Class OTPModel {
    private $con;

    public function __construct() {
        $this->con = DB::connect();
        if (session_status() == PHP_SESSION_NONE) {
            session_start();
        }
    }

    public function generateOTP($transactionID, $timeLife) {
        try {

            // Generate unique OTP
            do {
                $OTP = rand(100000, 999999);
                $sql = "SELECT * FROM `OTP` WHERE `OTP` = ?";
                $stmt = $this->con->prepare($sql);
                $stmt->bind_param("i", $OTP);
                $stmt->execute();
                $result = $stmt->get_result();
            } while ($result->num_rows > 0);
            
            $expired = date('Y-m-d H:i:s', strtotime("+$timeLife minutes"));
            $sql = "INSERT INTO `OTP` (`transactionID`, `OTP`, `expired`) VALUES (?, ?, ?)";
            $stmt = $this->con->prepare($sql);
            $stmt->bind_param("iis", $transactionID, $OTP, $expired);
            $stmt->execute();
            
            return $OTP;
            
        } catch (Exception $e) {
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }

    public function verifyOTP($transactionID, $OTP) {
        try {
            $sql = "SELECT * FROM `OTP` WHERE `transactionID` = ? AND `OTP` = ?";
            $stmt = $this->con->prepare($sql);
            $stmt->bind_param("ii", $transactionID, $OTP);
            $stmt->execute();
            $result = $stmt->get_result();

            if ($result->num_rows == 0) {
                throw new Exception('Invalid OTP');
            }

            return ['success' => true];

        } catch (Exception $e) {
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }
}
?>