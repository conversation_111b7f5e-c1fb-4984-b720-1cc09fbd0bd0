<?php
// Function route
function route($controller, $queries) {
    if (session_status() == PHP_SESSION_NONE) {
        session_start();
    }

    if (!isset($_SESSION['studentID']) && !($controller === 'login' && ($queries['method'] === 'login' || $queries['method'] === 'index'))) {
        $controller = 'login';
        $queries['method'] = 'index';
        header("Location: /login");
    }

    if ($controller === '' || (isset($_SESSION['studentID']) && $controller === 'login' && $queries['method'] !== 'logout')) {
        $controller = 'home';
        $queries['method'] = 'index';
        header("Location: /home");
    }

    $controller = ucfirst($controller . "Controller");
    if (file_exists(ROOT."/controllers/" . $controller . ".php")) {
        require_once ROOT."/controllers/{$controller}.php";
        $controller = new $controller();
        if (method_exists($controller, $queries["method"])) {
            $method = $queries["method"];
            $controller->$method();
        } else {
            page_not_found();
        }
    } else {
        page_not_found();
    }
}

// Page not found
function page_not_found() {
    http_response_code(404);
    require_once ROOT."/views/404.php";
}
?>
