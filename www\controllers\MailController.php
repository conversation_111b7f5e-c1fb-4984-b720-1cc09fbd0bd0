<?php
//Import PHPMailer classes into the global namespace
//These must be at the top of your script, not inside a function
use <PERSON><PERSON><PERSON>ail<PERSON>\PHPMailer\PHPMailer;
use P<PERSON>Mailer\PHPMailer\Exception;

//Load Composer's autoloader (created by composer, not included with <PERSON><PERSON><PERSON>ail<PERSON>)
require ROOT.'/vendor/autoload.php';

Class MailController {
    public function index($email, $subject, $template) {

        //Create an instance; passing `true` enables exceptions
        $mail = new PHPMailer(true);

        try {
            //Server settings
            $mail->isSMTP();                                            //Send using SMTP
            $mail->CharSet    = 'UTF-8';
            $mail->Host       = 'smtp.gmail.com';                     //Set the SMTP server to send through
            $mail->SMTPAuth   = true;                                   //Enable SMTP authentication
            $mail->Username   = '<EMAIL>';                     //SMTP username
            $mail->Password   = 'nnqqrahfxhejigwh';                               //SMTP password
            $mail->SMTPSecure = PHPMailer::ENCRYPTION_SMTPS;            //Enable implicit TLS encryption
            $mail->Port       = 465;                                    //TCP port to connect to; use 587 if you have set `SMTPSecure = PHPMailer::ENCRYPTION_STARTTLS`

            //Recipients
            $mail->setFrom('<EMAIL>', 'iBanking');
            $mail->addAddress($email);                          //Add a recipient

            //Content
            $mail->isHTML(true);                                  //Set email format to HTML
            
            $mail->Subject = $subject;
            $mail->Body    = $template;

            $mail->send();
            echo json_encode(['success' => true, 'message' => 'Mail sent']);
        } catch (Exception $e) {
            echo json_encode(['success' => false, 'message' => "Mail could not be sent. Mailer Error: {$mail->ErrorInfo}"]);
        }
    }

    public function sendOTP() {
        $email = $_POST['email'];
        $otp = $_POST['otp'];
        $minutes = $_POST['minutes'] ?? 5;

        $subject = '[iBanking] OTP for transaction approval';
        $template = file_get_contents(ROOT.'/views/otp_template.html');
        $template = str_replace(['{{$otp}}', '{{$minutes}}'], [$otp, $minutes], $template);

        $this->index($email, $subject, $template);
    }

    public function sendTransactionSuccess() {
        $email = $_POST['email'];
        $amount = number_format($_POST['amount'], 0, ',', '.');
        $payee = $_POST['payee'];
        $date = new DateTime($_POST['date']);

        $subject = '[iBanking] Transactions success';
        $template = file_get_contents(ROOT.'/views/transaction_success_template.html');
        $template = str_replace(['{{$amount}}', '{{$payee}}', '{{$date}}'], [$amount, $payee, $date->format('d M Y, H:i A')], $template);

        $this->index($email, $subject, $template);
    }
}
?>