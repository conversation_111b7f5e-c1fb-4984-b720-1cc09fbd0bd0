<?php
require_once("DB.php");

Class TuitionModel {
    private $con;

    public function __construct() {
        $this->con = DB::connect();
        if (session_status() == PHP_SESSION_NONE) {
            session_start();
        }
    }

    public function updateBalanceAndTuition($transactionID) {
        try {
            $this->con->begin_transaction();

            $sql1 = "UPDATE `transactions` SET `status` = 'approved' WHERE `transactionID` = ?";
            $stmt1 = $this->con->prepare($sql1);
            $stmt1->bind_param("i", $transactionID);
            $stmt1->execute();

            $sql2 = "UPDATE `profiles`  
                    SET `balance` = `balance` - (SELECT `amount` FROM `transactions` WHERE `transactionID` = ?) 
                    WHERE `studentID` = (SELECT `payer` FROM `transactions` WHERE `transactionID` = ?)";
            $stmt2 = $this->con->prepare($sql2);
            $stmt2->bind_param("ii", $transactionID, $transactionID);
            $stmt2->execute();

            $sql3 = "UPDATE `profiles` 
                    SET `tuition` = `tuition` - (SELECT `amount` FROM `transactions` WHERE `transactionID` = ?) 
                    WHERE `studentID` = (SELECT `payee` FROM `transactions` WHERE `transactionID` = ?)";
            $stmt3 = $this->con->prepare($sql3);
            $stmt3->bind_param("ii", $transactionID, $transactionID);
            $stmt3->execute();

            $sql4 = "DELETE FROM `OTP` WHERE `transactionID` = ?";
            $stmt4 = $this->con->prepare($sql4);
            $stmt4->bind_param("i", $transactionID);
            $stmt4->execute();

            $this->con->commit();

            return ['success' => true];

        } catch (Exception $e) {
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }

    public function generateTransaction($studentID, $payee, $amount) {
        try {
            $sql = "INSERT INTO `transactions` (`payer`, `payee`, `amount`) VALUES (?, ?, ?)";
            $stmt = $this->con->prepare($sql);
            $stmt->bind_param("ss", $studentID, $payee, $amount);
            $stmt->execute();

            $transactionID = $stmt->insert_id;
            return ['success' => true, 'transactionID' => $transactionID];

        } catch (Exception $e) {
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }
}
?>
