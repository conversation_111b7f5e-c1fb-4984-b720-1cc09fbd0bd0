<?php
class DB {
    public static function connect() {
        static $con = null;
        if ($con === null) {
            $host = 'mysql-server';
            $dbName = 'iBanking';
            $username = 'root';
            $password = 'root';

            $con = new mysqli($host, $username, $password, $dbName);
            if ($con->connect_error) {
                die("Connection failed: " . $con->connect_error);
            }
        }
        return $con;
    }

    public static function close() {
        $con = self::connect();
        $con->close();
    }
}
?>